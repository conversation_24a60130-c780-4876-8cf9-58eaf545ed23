import express from 'express';
import cors from 'cors';
import { createProxyMiddleware } from 'http-proxy-middleware';

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:5173', 'https://localhost:5173'], // Vite dev server URLs
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-XSRF-Token', 'X-Correlation-Id', 'X-Device-Id', 'X-Client-Build', 'X-GA-SessionId', 'X-Requested-With', 'Cookie']
}));

// Parse JSON bodies
app.use(express.json());

// Eldorado API proxy
app.use('/api/eldorado', createProxyMiddleware({
  target: 'https://www.eldorado.gg',
  changeOrigin: true,
  pathRewrite: {
    '^/api/eldorado': '/api', // Remove /eldorado from the path
  },
  onProxyReq: (proxyReq, req, res) => {
    // Forward all headers from the original request
    Object.keys(req.headers).forEach(key => {
      if (req.headers[key]) {
        proxyReq.setHeader(key, req.headers[key] as string);
      }
    });

    // Ensure proper headers for Eldorado API
    proxyReq.setHeader('Origin', 'https://www.eldorado.gg');
    proxyReq.setHeader('Referer', 'https://www.eldorado.gg/');
    proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    console.log(`Proxying ${req.method} ${req.url} to ${proxyReq.getHeader('host')}${proxyReq.path}`);
    console.log('Headers:', proxyReq.getHeaders());
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`Response from Eldorado: ${proxyRes.statusCode}`);
  },
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Proxy error', details: err.message });
  }
}));

app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
});
